from flask import Flask, render_template, request, jsonify, redirect, url_for, send_from_directory
import pandas as pd
import os
import io
import chardet
from datetime import datetime
import json
import sys
import traceback

# 添加调试输出
print("开始初始化应用...")

app = Flask(__name__, static_folder="app/static", template_folder="app/templates")
app.config['UPLOAD_FOLDER'] = os.path.abspath('uploads')  # 使用绝对路径
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB最大文件大小
app.config['JSON_AS_ASCII'] = False  # 确保JSON可以正确处理中文

print(f"应用初始化完成, 上传文件夹: {app.config['UPLOAD_FOLDER']}")

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

def detect_encoding(file_path):
    """检测文件编码"""
    try:
        with open(file_path, 'rb') as f:
            result = chardet.detect(f.read())
        encoding = result.get('encoding', 'utf-8')
        if not encoding:
            encoding = 'utf-8'
        return encoding
    except Exception as e:
        print(f"检测文件编码时出错: {e}")
        return 'utf-8'  # 默认返回utf-8编码

def process_csv_file(file_path):
    """处理CSV文件，跳过前10行，从第11行开始读取数据"""
    try:
        print(f"开始处理CSV文件: {file_path}")
        print(f"文件是否存在: {os.path.exists(file_path)}")
        print(f"文件大小: {os.path.getsize(file_path)} 字节")
        
        # 尝试读取前20行查看文件内容
        with open(file_path, 'rb') as f:
            raw_content = f.read(1024)  # 读取前1KB内容
        print(f"文件前1KB内容(hex): {raw_content.hex()[:100]}...")
        
        encoding = detect_encoding(file_path)
        print(f"检测到文件编码: {encoding}")
        
        # 尝试先读取前几行查看内容
        try:
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                sample_lines = [next(f).strip() for _ in range(15) if f]
            print(f"文件前15行内容示例: {sample_lines}")
        except Exception as e:
            print(f"读取文件前几行时出错: {e}")
        
        # 尝试使用低级方法读取文件
        try:
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                lines = [line.strip() for line in f if line.strip()]
            
            # 跳过前10行
            data_lines = lines[10:]
            if not data_lines:
                raise ValueError("文件内容为空或格式不正确")
            
            # 假设第一行是列名
            header_line = data_lines[0]
            header = header_line.split(',')
            print(f"提取的列名: {header}")
            
            # 处理数据行
            data = []
            for line in data_lines[1:]:
                values = line.split(',')
                if len(values) >= len(header):
                    data.append(values[:len(header)])
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=header)
            print(f"使用低级方法读取成功，数据形状: {df.shape}")
        except Exception as e:
            print(f"使用低级方法读取出错: {e}")
            # 尝试使用pandas直接读取
            try:
                df = pd.read_csv(file_path, encoding=encoding, skiprows=10, on_bad_lines='skip')
                print(f"使用pandas读取CSV成功，数据形状: {df.shape}")
            except Exception as e2:
                print(f"使用pandas读取CSV也出错: {e2}")
                raise ValueError(f"无法读取CSV文件: {str(e2)}")
        
        # 打印DataFrame的前几行数据，查看实际内容
        print(f"DataFrame前5行数据:\n{df.head()}")
        print(f"CSV列名: {df.columns.tolist()}")
        
        # 列名映射
        column_count = len(df.columns)
        print(f"实际列数: {column_count}")
        
        # 根据列数定义适当的列名
        if column_count == 9:  # 处理9列的情况
            new_columns = ['记录时间', '分类', '收支类型', '金额', '备注', '账户', '来源', '标签', '额外字段']
            df.columns = new_columns
            print(f"使用9列的新列名: {new_columns}")
        else:  # 处理其他列数的情况
            expected_columns = ['记录时间', '分类', '收支类型', '金额', '备注', '账户', '来源', '标签']
            
            # 根据实际列数自动调整预期列名
            if column_count > len(expected_columns):
                # 如果实际列数更多，增加额外的列名
                for i in range(len(expected_columns), column_count):
                    expected_columns.append(f"额外字段{i-len(expected_columns)+1}")
            elif column_count < len(expected_columns):
                # 如果实际列数较少，截取预期列名
                expected_columns = expected_columns[:column_count]
            
            # 设置列名
            df.columns = expected_columns
            print(f"调整后的列名: {df.columns.tolist()}")
        
        # 处理日期格式
        try:
            df['记录时间'] = pd.to_datetime(df['记录时间'], errors='coerce')
            print("日期格式处理完成")
        except Exception as e:
            print(f"处理日期格式时出错: {e}")
        
        # 确保金额为数值类型
        try:
            df['金额'] = pd.to_numeric(df['金额'], errors='coerce')
            print("金额格式处理完成")
        except Exception as e:
            print(f"处理金额格式时出错: {e}")
        
        # 对数据进行处理，删除没有记录时间或金额的行
        df = df.dropna(subset=['记录时间', '金额'])
        print(f"删除无效行后的数据形状: {df.shape}")
        
        print(f"CSV处理成功，最终数据形状: {df.shape}")
        return df
    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        print(traceback.format_exc())
        return None

@app.route('/')
def index():
    """首页"""
    print("访问首页")
    return render_template('index.html')

@app.route('/test-file-io', methods=['GET'])
def test_file_io():
    """测试文件IO操作"""
    try:
        # 测试目录
        upload_dir = app.config['UPLOAD_FOLDER']
        test_file_path = os.path.join(upload_dir, 'test.txt')
        test_content = "这是一个测试文件\n" + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 创建目录
        os.makedirs(upload_dir, exist_ok=True)
        dir_status = f"目录创建状态: {os.path.exists(upload_dir)}"
        
        # 写入文件
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        write_status = f"文件写入状态: {os.path.exists(test_file_path)}"
        
        # 读取文件
        with open(test_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        read_status = f"文件读取状态: {'成功' if content == test_content else '失败'}"
        
        return jsonify({
            'status': 'success',
            'message': '文件IO测试成功',
            'details': {
                'dir_path': upload_dir,
                'file_path': test_file_path,
                'dir_status': dir_status,
                'write_status': write_status,
                'read_status': read_status,
                'content': content
            }
        })
    except Exception as e:
        error_msg = f"文件IO测试失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': error_msg,
            'traceback': traceback.format_exc()
        }), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    try:
        print("开始处理文件上传请求")
        if 'file' not in request.files:
            print("错误：没有文件上传")
            return jsonify({'error': '没有文件上传'}), 400
        
        file = request.files['file']
        print(f"收到文件: {file.filename}")
        
        if file.filename == '':
            print("错误：未选择文件")
            return jsonify({'error': '未选择文件'}), 400
        
        if file and file.filename.endswith('.csv'):
            # 确保上传目录存在
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
            print(f"上传目录: {app.config['UPLOAD_FOLDER']}")
            print(f"上传目录是否存在: {os.path.exists(app.config['UPLOAD_FOLDER'])}")
            
            # 使用安全的文件名
            from werkzeug.utils import secure_filename
            safe_filename = secure_filename(file.filename)
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)
            print(f"保存文件到: {file_path}")
            
            try:
                # 将上传的文件先保存到内存中
                file_content = file.read()
                print(f"读取文件内容，大小: {len(file_content)} 字节")
                
                # 将内容写入文件
                with open(file_path, 'wb') as f:
                    f.write(file_content)
                print(f"文件保存成功: {file_path}")
                print(f"文件是否存在: {os.path.exists(file_path)}")
                print(f"文件大小: {os.path.getsize(file_path)} 字节")
            except Exception as e:
                print(f"保存文件出错: {str(e)}")
                print(traceback.format_exc())
                return jsonify({'error': f'保存文件失败: {str(e)}'}), 500
            
            # 处理CSV文件
            df = process_csv_file(file_path)
            if df is None:
                print("文件处理失败")
                return jsonify({'error': '文件处理失败，请检查文件格式是否正确'}), 500
            
            try:
                # 将处理后的数据保存为临时JSON文件
                json_data = df.to_json(orient='records', force_ascii=False, date_format='iso')
                temp_json_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_data.json')
                print(f"保存JSON数据到: {temp_json_path}")
                
                with open(temp_json_path, 'w', encoding='utf-8') as f:
                    f.write(json_data)
                print("JSON数据保存成功")
            except Exception as e:
                print(f"保存JSON数据出错: {str(e)}")
                print(traceback.format_exc())
                return jsonify({'error': f'处理数据失败: {str(e)}'}), 500
            
            print("文件上传并处理成功")
            return jsonify({
                'message': '文件上传并处理成功',
                'redirect': url_for('analyze')
            })
        else:
            print(f"不支持的文件类型: {file.filename}")
            return jsonify({'error': '仅支持CSV文件'}), 400
    except Exception as e:
        print(f"处理上传请求时出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'服务器处理错误: {str(e)}'}), 500

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """访问上传的文件"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/analyze')
def analyze():
    """分析页面"""
    # 检查是否有处理过的数据
    temp_json_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_data.json')
    if not os.path.exists(temp_json_path):
        return redirect(url_for('index'))
    
    try:
        # 读取JSON数据
        with open(temp_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return render_template('analyze.html', data=data)
    except Exception as e:
        print(f"读取分析数据时出错: {str(e)}")
        print(traceback.format_exc())
        return render_template('error.html', error=str(e))

@app.route('/api/data')
def get_data():
    """提供API接口获取处理后的数据"""
    temp_json_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_data.json')
    if not os.path.exists(temp_json_path):
        return jsonify({'error': '没有数据可用'}), 404
    
    with open(temp_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return jsonify(data)

@app.route('/api/summary')
def get_summary():
    """获取账单汇总数据"""
    temp_json_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_data.json')
    if not os.path.exists(temp_json_path):
        return jsonify({'error': '没有数据可用'}), 404
    
    try:
        with open(temp_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        df = pd.DataFrame(data)
        
        # 收入和支出总计
        income = df[df['收支类型'].isin(['收入', '内部收入', '资金收入', '投资收入'])]['金额'].sum()
        expense = df[df['收支类型'].isin(['支出', '内部支出', '资金支出', '投资支出'])]['金额'].sum()
        
        # 按分类汇总
        category_summary = df.groupby('分类')['金额'].sum().to_dict()
        
        # 按收支类型汇总
        type_summary = df.groupby('收支类型')['金额'].sum().to_dict()
        
        # 按天统计趋势
        df['日期'] = pd.to_datetime(df['记录时间']).dt.date
        daily_trend = df.groupby('日期')['金额'].sum().to_dict()
        daily_trend = {str(k): v for k, v in daily_trend.items()}

        # 按月统计收支
        df['年月'] = pd.to_datetime(df['记录时间']).dt.to_period('M').astype(str)
        monthly_data = df.groupby(['年月', '收支类型'])['金额'].sum().unstack(fill_value=0)
        monthly_summary = {}
        for month in monthly_data.index:
            monthly_summary[month] = {
                'income': float(monthly_data.loc[month, monthly_data.columns.str.contains('收入')].sum()),
                'expense': float(monthly_data.loc[month, monthly_data.columns.str.contains('支出')].sum())
            }

        # 按账户统计
        account_summary = df.groupby('账户')['金额'].sum().to_dict()
        account_summary = {k: float(v) for k, v in account_summary.items() if k and str(k) != 'nan'}

        # 周收支热力图数据
        df['星期'] = pd.to_datetime(df['记录时间']).dt.dayofweek
        df['小时'] = pd.to_datetime(df['记录时间']).dt.hour
        weekly_heatmap = df.groupby(['小时', '星期']).size().reset_index(name='count')
        weekly_summary = [[int(row['小时']), int(row['星期']), int(row['count'])] for _, row in weekly_heatmap.iterrows()]

        # 收支累计趋势
        df_sorted = df.sort_values('记录时间')
        df_sorted['累计金额'] = df_sorted['金额'].cumsum()
        cumulative_trend = df_sorted.groupby('日期')['累计金额'].last().to_dict()
        cumulative_trend = {str(k): float(v) for k, v in cumulative_trend.items()}

        # 时段分析
        hourly_summary = df.groupby('小时').size().to_dict()
        hourly_summary = {str(k): int(v) for k, v in hourly_summary.items()}

        summary = {
            'total_income': float(income),
            'total_expense': float(expense),
            'balance': float(income - expense),
            'category_summary': category_summary,
            'type_summary': type_summary,
            'daily_trend': daily_trend,
            'monthly_summary': monthly_summary,
            'account_summary': account_summary,
            'weekly_summary': weekly_summary,
            'cumulative_trend': cumulative_trend,
            'hourly_summary': hourly_summary
        }
        
        return jsonify(summary)
    except Exception as e:
        print(f"生成汇总数据时出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'生成汇总数据失败: {str(e)}'}), 500

@app.route('/api/ranking')
def get_ranking():
    """获取账单排行榜数据"""
    temp_json_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_data.json')
    if not os.path.exists(temp_json_path):
        print("排行榜API: 无法找到数据文件")
        return jsonify({'error': '没有数据可用'}), 404
    
    try:
        print("排行榜API: 开始加载数据文件...")
        with open(temp_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        df = pd.DataFrame(data)
        print(f"排行榜API: 成功加载数据，形状: {df.shape}")
        
        # 检查必要的列是否存在
        required_columns = ['备注', '收支类型', '金额']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"排行榜API: 缺少必要的列: {missing_columns}, 可用列: {df.columns.tolist()}")
            return jsonify({'error': f'数据格式不正确，缺少必要的列: {missing_columns}'}), 500
        
        # 处理备注为空的情况
        df['备注'].fillna('其他', inplace=True)
        print("排行榜API: 填充空备注为'其他'")
        
        # 提取备注中的主要内容（去除订单号等信息）
        def extract_main_content(remark):
            if not isinstance(remark, str):
                return '其他'
            # 尝试提取主要内容（例如：从"分账-闲鱼币智能软件服务费(123456)扣款"中提取"闲鱼币智能软件服务费"）
            import re
            patterns = [
                r'^分账-(.+?)\(', # 匹配"分账-XXX("格式
                r'^(.+?)-',      # 匹配"XXX-"格式
                r'^(.+?)（',      # 匹配"XXX（"格式
                r'^(.+?)\(',     # 匹配"XXX("格式
                r'^(.+?)扣款',    # 匹配"XXX扣款"格式
                r'^(.+?)$',      # 直接使用全部内容
            ]
            
            for pattern in patterns:
                match = re.search(pattern, remark)
                if match:
                    content = match.group(1).strip()
                    # 如果提取内容太长，截取前20个字符
                    return content[:20] if len(content) > 20 else content
            
            return '其他'
        
        # 添加处理后的备注列
        print("排行榜API: 提取备注主要内容")
        df['处理后备注'] = df['备注'].apply(extract_main_content)
        
        # 确保金额为数值类型
        print("排行榜API: 转换金额为数值类型")
        df['金额'] = pd.to_numeric(df['金额'], errors='coerce').fillna(0)
        
        # 检查收支类型
        print(f"排行榜API: 收支类型值: {df['收支类型'].unique().tolist()}")
        
        # 按收支类型和处理后的备注分组计算金额总和
        print("排行榜API: 按收支类型和处理后备注分组汇总")
        grouped_data = df.groupby(['收支类型', '处理后备注'])['金额'].sum().reset_index()
        print(f"排行榜API: 分组后数据形状: {grouped_data.shape}")
        
        # 分别获取收入和支出的排行榜
        income_types = ['收入', '内部收入', '资金收入', '投资收入']
        expense_types = ['支出', '内部支出', '资金支出', '投资支出']
        
        # 使用isin函数前先确保所有值都是字符串
        grouped_data['收支类型'] = grouped_data['收支类型'].astype(str)
        
        # 收入数据
        income_data = grouped_data[grouped_data['收支类型'].isin(income_types)]
        print(f"排行榜API: 收入数据项数: {len(income_data)}")
        
        # 支出数据
        expense_data = grouped_data[grouped_data['收支类型'].isin(expense_types)]
        print(f"排行榜API: 支出数据项数: {len(expense_data)}")
        
        # 如果没有明确的收入或支出类别，尝试根据金额正负来区分
        if len(income_data) == 0 and len(expense_data) == 0:
            print("排行榜API: 未找到明确的收支类型，尝试根据金额正负区分")
            income_data = grouped_data[grouped_data['金额'] > 0]
            expense_data = grouped_data[grouped_data['金额'] < 0]
            print(f"排行榜API: 重新分类后 - 收入数据项数: {len(income_data)}, 支出数据项数: {len(expense_data)}")
        
        # 排序并获取所有数据（不限制数量）
        all_income = income_data.sort_values('金额', ascending=False).to_dict('records')
        all_expense = expense_data.sort_values('金额', ascending=False).to_dict('records')
        
        # 如果支出金额为负数，转换为正数方便前端展示
        for item in all_expense:
            if item['金额'] < 0:
                item['金额'] = abs(item['金额'])

        result = {
            'income': all_income,
            'expense': all_expense
        }

        print(f"排行榜API: 返回结果 - 收入项数: {len(all_income)}, 支出项数: {len(all_expense)}")
        return jsonify(result)
    except Exception as e:
        print(f"生成排行榜数据时出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'生成排行榜数据失败: {str(e)}'}), 500

# 添加全局错误处理
@app.errorhandler(500)
def internal_server_error(e):
    return jsonify({
        'error': '服务器内部错误',
        'message': str(e)
    }), 500

@app.errorhandler(404)
def page_not_found(e):
    return jsonify({
        'error': '页面不存在',
        'message': str(e)
    }), 404

# 创建错误页面模板
@app.route('/create-error-template')
def create_error_template():
    error_template_path = os.path.join(app.template_folder, 'error.html')
    if not os.path.exists(error_template_path):
        with open(error_template_path, 'w', encoding='utf-8') as f:
            f.write('''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 - 账单分析系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "Microsoft YaHei", sans-serif;
            padding-top: 50px;
        }
        .error-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 6px 10px rgba(0,0,0,0.08);
            text-align: center;
        }
        .error-icon {
            font-size: 80px;
            color: #dc3545;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h1 class="mb-4">发生错误</h1>
            <p class="text-muted mb-4">{{ error }}</p>
            <a href="/" class="btn btn-primary">返回首页</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>''')
    return jsonify({'message': '错误页面模板创建成功'})

print("开始启动Flask应用...")

if __name__ == '__main__':
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"模板目录: {os.path.join(os.getcwd(), 'app/templates')}")
    print(f"模板目录是否存在: {os.path.exists(os.path.join(os.getcwd(), 'app/templates'))}")
    print(f"index.html是否存在: {os.path.exists(os.path.join(os.getcwd(), 'app/templates/index.html'))}")
    print(f"analyze.html是否存在: {os.path.exists(os.path.join(os.getcwd(), 'app/templates/analyze.html'))}")
    print(f"上传目录: {app.config['UPLOAD_FOLDER']}")
    print(f"上传目录是否存在: {os.path.exists(app.config['UPLOAD_FOLDER'])}")
    app.run(debug=True) 