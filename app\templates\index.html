<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账单分析系统 - 数据上传</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-light: rgba(67, 97, 238, 0.1);
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --success-color: #4ade80;
            --danger-color: #f87171;
            --bg-color: #f8fafc;
            --card-bg: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #cbd5e1;
        }

        body {
            background-color: var(--bg-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow-x: hidden;
        }

        .bg-decoration {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(76, 201, 240, 0.15) 0%, rgba(67, 97, 238, 0.08) 100%);
            z-index: -1;
            filter: blur(25px);
        }

        .bg-decoration-1 {
            top: -100px;
            left: -150px;
            width: 450px;
            height: 450px;
        }

        .bg-decoration-2 {
            bottom: -150px;
            right: -100px;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, rgba(76, 201, 240, 0.1) 0%, rgba(74, 222, 128, 0.08) 100%);
        }

        .bg-decoration-3 {
            top: 40%;
            right: 25%;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(74, 222, 128, 0.12) 0%, rgba(76, 201, 240, 0.06) 100%);
        }

        .container {
            max-width: 1000px;
            padding: 30px;
        }

        .app-card {
            background-color: var(--card-bg);
            border-radius: 24px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.4s ease;
            position: relative;
        }

        .app-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(67, 97, 238, 0.15);
        }

        .card-header-bg {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            height: 140px;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .card-header-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.2;
        }

        .card-header-bg::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background: linear-gradient(to top, var(--card-bg), transparent);
            z-index: -1;
        }

        .card-body {
            position: relative;
            margin-top: -60px;
            z-index: 2;
            padding: 35px;
        }

        .app-title {
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 5px;
            letter-spacing: -0.5px;
        }

        .app-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .upload-zone {
            border: 2px dashed var(--border-color);
            border-radius: 16px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 30px;
            background-color: #f8fafc;
            position: relative;
            overflow: hidden;
        }

        .upload-zone:hover, .upload-zone.drag-over {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
            transform: scale(1.01);
        }

        .upload-zone:hover .upload-icon, .upload-zone.drag-over .upload-icon {
            transform: scale(1.1) translateY(-5px);
        }

        .upload-icon {
            font-size: 3.5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .upload-text {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 15px;
        }

        .file-types {
            font-size: 0.9rem;
            color: var(--text-muted);
            position: relative;
            display: inline-block;
        }

        .file-types::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background-color: var(--accent-color);
            border-radius: 3px;
        }

        .file-input {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0;
            cursor: pointer;
        }

        .file-details {
            background-color: #f1f5f9;
            border-radius: 16px;
            padding: 18px 25px;
            margin-bottom: 28px;
            display: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .file-details.show {
            display: flex;
            align-items: center;
            animation: fadeInUp 0.4s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .file-icon {
            font-size: 2.2rem;
            color: var(--primary-color);
            margin-right: 18px;
        }

        .file-info {
            flex-grow: 1;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 350px;
        }

        .file-size {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .file-remove {
            color: var(--danger-color);
            cursor: pointer;
            font-size: 1.4rem;
            transition: all 0.2s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .file-remove:hover {
            background-color: rgba(248, 113, 113, 0.1);
            transform: rotate(90deg);
        }

        .progress-container {
            margin-bottom: 25px;
            display: none;
        }

        .progress {
            height: 12px;
            border-radius: 6px;
            background-color: #e2e8f0;
            margin-bottom: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            transition: width 0.3s ease;
            height: 100%;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 30px 30px;
            animation: progress-animation 2s linear infinite;
            z-index: 1;
        }

        @keyframes progress-animation {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 30px 0;
            }
        }

        .instructions-title {
            color: var(--text-primary);
            font-weight: 700;
            position: relative;
            padding-bottom: 12px;
            margin-bottom: 20px;
        }

        .instructions-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 4px;
            background: linear-gradient(to right, var(--accent-color), var(--primary-color));
            border-radius: 2px;
        }

        .instruction-item {
            background-color: transparent;
            border: none;
            border-left: 3px solid var(--accent-color);
            border-radius: 0 8px 8px 0;
            padding: 12px 18px;
            margin-bottom: 10px;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        }

        .instruction-item:hover {
            background-color: var(--primary-light);
            transform: translateX(5px);
        }

        .instruction-item i {
            color: var(--primary-color);
        }

        .feature-card {
            background-color: white;
            border-radius: 16px;
            padding: 20px 15px;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(67, 97, 238, 0.1);
        }

        .feature-icon {
            font-size: 2.2rem;
            color: var(--primary-color);
            margin-bottom: 15px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.2);
            color: var(--secondary-color);
        }

        .feature-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .feature-text {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 28px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn-primary::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
            z-index: -1;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }

        .btn-primary:hover::after {
            left: 100%;
        }

        .btn-primary:disabled {
            background: linear-gradient(135deg, #94a3b8, #cbd5e1);
            transform: none;
            box-shadow: none;
        }

        .alert {
            border-radius: 12px;
            padding: 16px 22px;
            margin-bottom: 25px;
            display: none;
            animation: fadeInUp 0.4s ease forwards;
            position: relative;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .alert-danger {
            background-color: rgba(248, 113, 113, 0.15);
            border-left: 4px solid var(--danger-color);
            color: #b91c1c;
        }

        .alert-success {
            background-color: rgba(74, 222, 128, 0.15);
            border-left: 4px solid var(--success-color);
            color: #15803d;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .card-body {
                padding: 25px 20px;
            }
            
            .app-title {
                font-size: 1.8rem;
            }
            
            .upload-zone {
                padding: 25px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 背景装饰 -->
    <div class="bg-decoration bg-decoration-1"></div>
    <div class="bg-decoration bg-decoration-2"></div>
    <div class="bg-decoration bg-decoration-3"></div>

    <div class="container">
        <div class="app-card animate__animated animate__fadeInUp">
            <div class="card-header-bg"></div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-7">
                        <h1 class="app-title">账单分析系统</h1>
                        <p class="app-subtitle">上传您的CSV账单文件，获取专业的收支分析</p>
                        
                        <!-- 上传区域 -->
                        <div class="upload-zone" id="uploadZone">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-arrow-up-fill"></i>
                            </div>
                            <h5 class="upload-text">点击或拖拽文件到此处上传</h5>
                            <p class="file-types">支持格式: CSV</p>
                            <input type="file" class="file-input" id="fileInput" accept=".csv">
                        </div>
                        
                        <!-- 文件详情 -->
                        <div class="file-details" id="fileDetails">
                            <div class="file-icon">
                                <i class="bi bi-file-earmark-spreadsheet-fill"></i>
                            </div>
                            <div class="file-info">
                                <div class="file-name" id="fileName">文件名.csv</div>
                                <div class="file-size" id="fileSize">0 KB</div>
                            </div>
                            <div class="file-remove" id="fileRemove">
                                <i class="bi bi-x-lg"></i>
                            </div>
                        </div>
                        
                        <!-- 进度条 -->
                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar" id="progressBar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <small class="text-muted" id="progressText">上传中... 0%</small>
                        </div>
                        
                        <!-- 提示信息 -->
                        <div class="alert alert-danger" id="errorAlert" role="alert"></div>
                        <div class="alert alert-success" id="successAlert" role="alert"></div>
                        
                        <!-- 上传按钮 -->
                        <button type="button" class="btn btn-primary btn-lg w-100" id="uploadBtn" disabled>
                            <i class="bi bi-lightning-charge-fill me-2"></i>开始分析
                        </button>
                    </div>
                    
                    <div class="col-lg-5 mt-4 mt-lg-0">
                        <h5 class="instructions-title">使用说明</h5>
                        <ul class="list-group mb-4">
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-1-circle-fill me-2"></i>选择或拖拽您的CSV账单文件
                            </li>
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-2-circle-fill me-2"></i>系统将自动从第11行开始读取数据
                            </li>
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-3-circle-fill me-2"></i>点击"开始分析"按钮上传文件
                            </li>
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-4-circle-fill me-2"></i>等待分析完成，系统会自动展示结果
                            </li>
                        </ul>
                        
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-graph-up-arrow"></i>
                                    </div>
                                    <h6 class="feature-title">数据可视化</h6>
                                    <p class="feature-text">直观图表展示收支情况</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-bar-chart-fill"></i>
                                    </div>
                                    <h6 class="feature-title">排行榜分析</h6>
                                    <p class="feature-text">了解主要支出和收入来源</p>
                                </div>
                            </div>
                            <div class="col-6 mt-3">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-calendar-check-fill"></i>
                                    </div>
                                    <h6 class="feature-title">时间趋势</h6>
                                    <p class="feature-text">掌握收支时间规律</p>
                                </div>
                            </div>
                            <div class="col-6 mt-3">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-search"></i>
                                    </div>
                                    <h6 class="feature-title">明细查询</h6>
                                    <p class="feature-text">快速搜索账单记录</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const fileDetails = document.getElementById('fileDetails');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            const fileRemove = document.getElementById('fileRemove');
            const uploadZone = document.getElementById('uploadZone');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const errorAlert = document.getElementById('errorAlert');
            const successAlert = document.getElementById('successAlert');

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes < 1024) return bytes + ' B';
                else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
                else return (bytes / 1048576).toFixed(2) + ' MB';
            }

            // 文件选择处理
            fileInput.addEventListener('change', function() {
                handleFileSelection(this.files);
            });

            // 拖放功能
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadZone.addEventListener(eventName, function() {
                    uploadZone.classList.add('drag-over');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, function() {
                    uploadZone.classList.remove('drag-over');
                }, false);
            });

            uploadZone.addEventListener('drop', function(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFileSelection(files);
            }, false);

            // 处理文件选择
            function handleFileSelection(files) {
                if (files.length > 0) {
                    const file = files[0];
                    
                    if (file.name.toLowerCase().endsWith('.csv')) {
                        uploadBtn.disabled = false;
                        fileName.textContent = file.name;
                        fileSize.textContent = formatFileSize(file.size);
                        fileDetails.classList.add('show');
                        
                        // 添加动画效果
                        uploadZone.classList.add('animate__animated', 'animate__pulse');
                        setTimeout(() => {
                            uploadZone.classList.remove('animate__animated', 'animate__pulse');
                        }, 800);
                    } else {
                        showError('请选择CSV格式的文件');
                        resetFileInput();
                    }
                }
            }

            // 移除文件
            fileRemove.addEventListener('click', function() {
                resetFileInput();
            });

            // 重置文件输入
            function resetFileInput() {
                fileInput.value = '';
                fileDetails.classList.remove('show');
                uploadBtn.disabled = true;
            }

            // 点击上传按钮
            uploadBtn.addEventListener('click', function() {
                if (!fileInput.files[0]) {
                    showError('请先选择文件');
                    return;
                }

                const file = fileInput.files[0];
                const formData = new FormData();
                formData.append('file', file);

                // 重置并显示进度条
                hideAlerts();
                progressContainer.style.display = 'block';
                updateProgress(0);
                uploadBtn.disabled = true;

                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    if (progress <= 90) {
                        updateProgress(progress);
                    } else {
                        clearInterval(interval);
                    }
                }, 100);

                // 发送上传请求
                fetch('/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    clearInterval(interval);
                    updateProgress(100);
                    
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '上传失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    showSuccess(data.message || '上传成功，正在分析数据...');
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                })
                .catch(error => {
                    showError(error.message);
                    progressContainer.style.display = 'none';
                    uploadBtn.disabled = false;
                });
            });

            // 更新进度条
            function updateProgress(value) {
                progressBar.style.width = value + '%';
                progressBar.setAttribute('aria-valuenow', value);
                progressText.textContent = `上传中... ${value}%`;
            }

            // 显示错误信息
            function showError(message) {
                hideAlerts();
                errorAlert.textContent = message;
                errorAlert.style.display = 'block';
            }

            // 显示成功信息
            function showSuccess(message) {
                hideAlerts();
                successAlert.textContent = message;
                successAlert.style.display = 'block';
            }

            // 隐藏所有提示
            function hideAlerts() {
                errorAlert.style.display = 'none';
                successAlert.style.display = 'none';
            }
        });
    </script>
</body>
</html> 